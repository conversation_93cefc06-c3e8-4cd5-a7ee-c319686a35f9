package com.mall.project.controller.transactionDataTotalController;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.transactionDataTotal.zNHTradeData;
import com.mall.project.service.transactionDataTotal.TransactionDataTotalService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 累计交易数据控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class TransactionDataTotalController {

    @Autowired
    private TransactionDataTotalService transactionDataTotalService;

    /**
     *  查询统计累计交易数据 分页显示
     */
    @PostMapping("/QuerytransactionDataTotalPages")
    public CommonResult<CommonPage<Map<String, Object>>> QuerytransactionDataTotalPages(@RequestBody @Valid zNHTradeData param) {
        CommonPage<Map<String, Object>> commonPage = transactionDataTotalService.QuerytransactionDataTotalPages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    // 导出 累计交易数据 Excel
    @PostMapping("/transactionDataTotalExport")
    public void transactionDataTotalExportPojo(HttpServletResponse response,zNHTradeData param) {
        try {
            // 获取累计交易数据
            List<Map<String, Object>> dataList = transactionDataTotalService.transactionDataTotalExport(param.getStartDate(), param.getEndDate());
            String sumTradeAmountTotal = String.valueOf(transactionDataTotalService.sumTradeAmountTotal());

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateTime", "updateTime");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("enterpriseName", "enterpriseName");
            fieldMapping.put("tradeName", "tradeName");
            fieldMapping.put("tradeAmount", "tradeAmountSum");

            // 配置汇总信息（一行显示一个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计交易总金额")
                        .value(sumTradeAmountTotal)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<zNHTradeData> config = UniversalExcelExporter.ExportConfig.<zNHTradeData>builder()
                    .dataList(dataList)
                    .entityClass(zNHTradeData.class)
                    .fileName("累计交易数据")
                    .sheetName("累计交易数据")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出累计交易数据异常", e);
        }
    }
}
