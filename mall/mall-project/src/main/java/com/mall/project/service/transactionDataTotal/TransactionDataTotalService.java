package com.mall.project.service.transactionDataTotal;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 累计交易数据服务接口
 */
public interface TransactionDataTotalService {
    /**
     *  查询统计累计交易数据 分页显示
     */
    public CommonPage<Map<String, Object>> QuerytransactionDataTotalPages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 获取累计交易金额
     */
    public double sumTradeAmountTotal();

    /**
     * 导出 累计交易数据 Excel
     */
    List<Map<String, Object>> transactionDataTotalExport(String startDate, String endDate);
}
