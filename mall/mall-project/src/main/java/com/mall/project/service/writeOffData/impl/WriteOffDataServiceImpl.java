package com.mall.project.service.writeOffData.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.writeOffData.WriteOffDataDao;

import com.mall.project.exception.BusinessException;
import com.mall.project.service.writeOffData.WriteOffDataService;
import com.mall.project.util.MallBAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 核销数据服务实现类
 */
@Service
@Slf4j
public class WriteOffDataServiceImpl implements WriteOffDataService {
    @Autowired
    private WriteOffDataDao writeOffDataDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 获取mallB系统的核销数据
     */
    @Override
    public void getWriteOffDataFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取核销数据
            ResponseEntity<String> response = mallBAuthUtils.getForEntity("/mall/receptionA/getUserDeductionStat", String.class);

            // 检查获取核销数据是否成功
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB核销数据失败: " + response.getStatusCode());
            }

            // 解析核销数据响应
            String responseBody = response.getBody();
            if (responseBody == null) {
                throw new BusinessException("mallB核销数据为空");
            }

            // 解析响应JSON
            JsonNode root = objectMapper.readTree(responseBody);

            if (root.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB核销数据失败: " + root.get("msg").asText());
            }

            // 获取mallB核销数据列表
            JsonNode dataList = root.path("data");
            if (!dataList.isArray() || dataList.isEmpty()) {
                log.info("mallB核销数据为空");
                return;
            }

            // 存储核销数据到本地系统
            log.info("开始处理并存储mallB核销数据，共 {} 条记录", dataList.size());

            // 遍历核销数据并存储
            for (JsonNode dataItem : dataList) {
                try {
                    String updateDate = dataItem.path("updateDate").asText();
                    String phone = dataItem.path("phone").asText();
                    String writeOffSubsidy = dataItem.path("writeOffSubsidy").asText();
                    String writeOffSubsidyTotal = dataItem.path("writeOffSubsidyTotal").asText();
                    String unWriteOffSubsidy = dataItem.path("unWriteOffSubsidy").asText();

                    // 调用DAO层存储数据
                    writeOffDataDao.saveWriteOffData(updateDate, phone, writeOffSubsidy, writeOffSubsidyTotal, unWriteOffSubsidy);

                } catch (Exception e) {
                    log.error("处理mallB核销数据项失败: {}", e.getMessage(), e);
                    // 继续处理下一条记录，不要直接返回
                }
            }
            log.info("成功完成mallB核销数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 查询核销数据,分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryWriteOffDataPages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (isNotEmpty(startDate) && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (isNotEmpty(endDate) && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = writeOffDataDao.queryWriteOffDataPages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = writeOffDataDao.totalWriteOffData(phone, startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        return new CommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
    }
    /**
     * 导出核销数据 Excel
     */
    @Override
    public List<Map<String, Object>> exportWriteOffDataExcel(String startDate, String endDate) {
        List<Map<String, Object>> dataList = writeOffDataDao.exportWriteOffDataExcel(startDate, endDate);
        if(dataList.isEmpty()){
            throw new RuntimeException("暂无数据");
        }
        // 转换下划线格式为驼峰格式
        return dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }

    /**
     * 统计 今日核销补贴金
     */
    @Override
    public String todayTotalWriteOffSubsidy() {
        return writeOffDataDao.todayTotalWriteOffSubsidy() == null ? "0" : writeOffDataDao.todayTotalWriteOffSubsidy();
    }

    /**
     * 统计 累计核销补贴金
     */
    @Override
    public String totalWriteOffSubsidy() {
        return writeOffDataDao.totalWriteOffSubsidy() == null ? "0" : writeOffDataDao.totalWriteOffSubsidy();
    }

    /**
     * 统计 累计未核销补贴金
     */
    @Override
    public String totalUnWriteOffSubsidy() {
        return writeOffDataDao.totalUnWriteOffSubsidy() == null ? "0" : writeOffDataDao.totalUnWriteOffSubsidy();
    }
}
