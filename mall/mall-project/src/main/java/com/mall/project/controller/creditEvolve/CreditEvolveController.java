package com.mall.project.controller.creditEvolve;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.creditEvolve.CreditEvolve;
import com.mall.project.service.creditEvolve.CreditEvolveService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信用值进化量控制器
 */
@RestController
@Slf4j
public class CreditEvolveController {
    @Autowired
    private CreditEvolveService creditEvolveService;

    /**
     * 查询信用值进化量,分页显示
     */
    @PostMapping("/queryCreditEvolvePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryCreditEvolvePages(@RequestBody @Valid CreditEvolve param) {
        CommonPage<Map<String, Object>> commonPage = creditEvolveService.queryCreditEvolvePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    /**
     * 今日总信用值进化量
     */
    @GetMapping("/todayTotalCreditEvolve")
    public CommonResult<String> todayTotalCreditEvolve() {
        String todayTotalCreditEvolve = creditEvolveService.todayTotalCreditEvolve();
        if (!todayTotalCreditEvolve.isEmpty()) {
            return CommonResult.success(todayTotalCreditEvolve);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 累计信用值进化量
     */
    @GetMapping("/totalCreditEvolve")
    public CommonResult<String> totalCreditEvolve() {
        String totalCreditEvolve = creditEvolveService.totalCreditEvolve();
        if (!totalCreditEvolve.isEmpty()) {
            return CommonResult.success(totalCreditEvolve);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 信用值进化量, 导出 Excel
     */
    @PostMapping("/exportCreditEvolveExcel")
    public void exportCreditEvolveExcel(HttpServletResponse response, CreditEvolve param) {
        try {
            // 获取信用值进化量数据
            List<Map<String, Object>> dataList = creditEvolveService.exportCreditEvolveExcel(param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String todayTotalCreditEvolve = creditEvolveService.todayTotalCreditEvolve();
            String totalCreditEvolve = creditEvolveService.totalCreditEvolve();

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("userName", "username");
            fieldMapping.put("creditEvolve", "creditEvolve");
            fieldMapping.put("creditEvolveTotal", "creditEvolveTotal");

            // 配置汇总信息（一行显示两个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日信用值进化量")
                        .value(todayTotalCreditEvolve)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计信用值进化量")
                        .value(totalCreditEvolve)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<CreditEvolve> config = UniversalExcelExporter.ExportConfig.<CreditEvolve>builder()
                    .dataList(dataList)
                    .entityClass(CreditEvolve.class)
                    .fileName("信用值进化量")
                    .sheetName("信用值进化量")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出信用值进化量异常", e);
        }
    }
}
