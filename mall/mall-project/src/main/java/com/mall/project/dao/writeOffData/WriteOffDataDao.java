package com.mall.project.dao.writeOffData;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 核销数据数据访问对象
 */
@Repository
public class WriteOffDataDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询核销数据,分页显示
     */
    public List<Map<String, Object>> queryWriteOffDataPages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,phone,write_off_subsidy,write_off_subsidy_total,un_write_off_subsidy FROM write_off_data WHERE update_date <= CURDATE() - INTERVAL 1 DAY";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date >= ?";
            params.add(startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(endDate);
        }
        sql += " ORDER BY update_date DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    public int totalWriteOffData(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM write_off_data WHERE update_date <= CURDATE() - INTERVAL 1 DAY";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date >= ?";
            params.add(startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(endDate);
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 导出核销数据 Excel
     */
    public List<Map<String, Object>> exportWriteOffDataExcel(String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,phone,write_off_subsidy,write_off_subsidy_total,un_write_off_subsidy FROM write_off_data WHERE update_date <= CURDATE() - INTERVAL 1 DAY";
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date >= ?";
            params.add(startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(endDate);
        }
        sql += " ORDER BY update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 统计 今日核销补贴金
     */
    public String todayTotalWriteOffSubsidy(){
        String sql = "SELECT sum(write_off_subsidy) as today_total_write_off_subsidy FROM write_off_data WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 统计 累计核销补贴金
     */
    public String totalWriteOffSubsidy(){
        String sql = "SELECT sum(write_off_subsidy_total) as total_write_off_subsidy FROM write_off_data";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 统计 累计未核销补贴金
     */
    public String totalUnWriteOffSubsidy(){
        String sql = "SELECT sum(un_write_off_subsidy) as total_un_write_off_subsidy FROM write_off_data";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 保存核销数据 - 如果该手机号当天已有数据则更新，否则插入新数据
     */
    public void saveWriteOffData(String updateDate, String phone, String writeOffSubsidy, String writeOffSubsidyTotal, String unWriteOffSubsidy){
        // 先检查该手机号当天是否已有数据
        String checkSql = "SELECT COUNT(*) FROM write_off_data WHERE phone = ? AND update_date = ?";
        int count = jdbcTemplate.queryForObject(checkSql, Integer.class, phone, updateDate);

        if (count > 0) {
            // 如果已存在，则更新数据
            String updateSql = "UPDATE write_off_data SET write_off_subsidy = ?, write_off_subsidy_total = ?, un_write_off_subsidy = ? " +
                              "WHERE phone = ? AND update_date = ?";
            jdbcTemplate.update(updateSql, writeOffSubsidy, writeOffSubsidyTotal, unWriteOffSubsidy, phone, updateDate);
        } else {
            // 如果不存在，则插入新数据
            String insertSql = "INSERT INTO write_off_data(update_date, phone, write_off_subsidy, write_off_subsidy_total, un_write_off_subsidy) " +
                              "VALUES (?, ?, ?, ?, ?)";
            jdbcTemplate.update(insertSql, updateDate, phone, writeOffSubsidy, writeOffSubsidyTotal, unWriteOffSubsidy);
        }
    }
}
