package com.mall.project.service.adminQuantifyEvolve.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.adminQuantifyEvolve.AdminQuantifyEvolveDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.adminQuantifyEvolve.AdminQuantifyEvolveService;
import com.mall.project.util.MallBAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Admin量化值进化量 服务实现类
 */
@Service
@Slf4j
public class AdminQuantifyEvolveServiceImpl implements AdminQuantifyEvolveService {
    @Autowired
    private AdminQuantifyEvolveDao adminQuantifyEvolveDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 去mallB系统读取 Admin量化值进化量
     */
    @Override
    public void getAdminQuantifyEvolveFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();
            ResponseEntity<String> adminQuantifyResponse = mallBAuthUtils.getForEntity("/mall/receptionA/statistics/adminQuantification", String.class);

            // 检查获取Admin量化值进化量数据是否成功
            if (adminQuantifyResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB系统Admin量化值进化量数据失败: " + adminQuantifyResponse.getStatusCode());
            }

            // 解析Admin量化值进化量数据响应
            String adminQuantifyResponseBody = adminQuantifyResponse.getBody();
            if (adminQuantifyResponseBody == null) {
                throw new BusinessException("获取mallB系统Admin量化值进化量数据响应为空");
            }

            // 解析响应JSON
            JsonNode adminQuantifyRoot = objectMapper.readTree(adminQuantifyResponseBody);

            if (adminQuantifyRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB系统Admin量化值进化量数据失败: " + adminQuantifyRoot.get("msg").asText());
            }

            // 获取Admin量化值进化量数据
            JsonNode dataNode = adminQuantifyRoot.path("data");
            if (dataNode.isMissingNode() || dataNode.isNull()) {
                log.info("今日mallB系统无Admin量化值进化量数据");
                return;
            }

            // 从data节点中提取数据
            String phone = dataNode.path("phone").asText();
            double value = dataNode.path("dayConvertAmount").asDouble();
            String updateDate = dataNode.path("date").asText();

            //log.info("开始处理并存储mallB Admin量化值进化量数据: phone={}, value={}, date={}", phone, value, updateDate);

            // 最后保存数据
            adminQuantifyEvolveDao.saveOrUpdateAdminQuantifyEvolve(phone, String.valueOf(value), updateDate);

            //log.info("成功完成mallB系统Admin量化值进化量数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 查询Admin量化值, 分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryAdminAdminQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = adminQuantifyEvolveDao.queryAdminAdminQuantifyEvolvePages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedDataList = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = adminQuantifyEvolveDao.totalAdminQuantifyEvolveCount(phone, startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        return new CommonPage<>(pageNum, pageSize, totalPages, total, formattedDataList);
    }

    /**
     * 累计Admin量化值进化量
     */
    @Override
    public String totalAdminQuantifyEvolve() {
        return adminQuantifyEvolveDao.totalAdminQuantifyEvolve() == null ? "0" : adminQuantifyEvolveDao.totalAdminQuantifyEvolve();
    }

    /**
     * 导出Admin量化值进化量 Excel
     */
    @Override
    public List<Map<String, Object>> exportAdminAdminQuantifyEvolveExcel(String startDate, String endDate) {
        if(startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if(endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 转换下划线格式为驼峰格式
        return adminQuantifyEvolveDao.exportAdminAdminQuantifyEvolveExcel(startDate, endDate).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }
}
