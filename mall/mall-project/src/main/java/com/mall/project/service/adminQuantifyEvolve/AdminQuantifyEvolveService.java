package com.mall.project.service.adminQuantifyEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * Admin量化值进化量 Service
 */
public interface AdminQuantifyEvolveService {

    /**
     * 去mallB系统读取 Admin量化值进化量
     */
    public void getAdminQuantifyEvolveFromMallB();

    /**
     * 查询Admin量化值进化量, 分页显示
     */
    public CommonPage<Map<String, Object>> queryAdminAdminQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 累计Admin量化值进化量
     */
    public String totalAdminQuantifyEvolve();

    /**
     * 导出Admin量化值进化量 Excel
     */
    public List<Map<String, Object>> exportAdminAdminQuantifyEvolveExcel(String startDate, String endDate);
}
