package com.mall.project.service.quantizationValue;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 描述：量化值服务接口
 */
public interface QuantizationValueService {

    /**
     * 量化值计算
     */
    public void updateQuantizationValue();

    /**
     * 计算 每日Admin量化值
     */
    public String adminDailyQuantifyValue(String startTime);

    /**
     * 计算 每日累计Admin量化值
     */
    public String adminDailyQuantizationValue(String startTime);

    /**
     * 查询量化值, 分页显示
     */
    public CommonPage<Map<String, Object>> queryQuantizationValuePages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 导出量化值 Excel
     */
    public List<Map<String, Object>> exportQuantizationValueExcel(String phone,String startDate, String endDate);

    /**
     * 每日平台补贴金
     */
    public String todayTotalPlatformGold(String startTime);

    /**
     * 累计平台补贴金
     */
    public String totalPlatformGold(String startTime);

    /**
     * 每日平台促销金
     */
    public String todayTotalPromotionGold(String startTime);

    /**
     * 累计平台促销金
     */
    public String totalPromotionGold(String startTime);

    /**
     * 每日量化值累计
     */
    public String everydayTotalQuantizationValue(String startTime);

    /**
     * 总量化值累计
     */
    public String allTotalQuantizationValue(String startTime);

    /**
     * 每日量化累计
     */
    public String everydayTotalCreditValue(String startTime);

    /**
     * 总量化累计
     */
    public String allTotalCreditValue(String startTime);

    /**
     * 查询每日量化值,并推送到mallB系统
     */
    public List<Map<String, Object>> queryTodayQuantizationValue();

    /**
     * 查询每日平台补贴金,并推送到mallB系统
     */
    public List<Map<String, Object>> queryTodayPlatformGold();


    /**
     * 查询最新日期的量化率
     */
    public String getLatestQuantizationRate(String startTime);
}
