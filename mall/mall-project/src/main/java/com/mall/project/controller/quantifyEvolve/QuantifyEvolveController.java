package com.mall.project.controller.quantifyEvolve;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.quantifyEvolve.QuantifyEvolve;
import com.mall.project.service.quantifyEvolve.QuantifyEvolveService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 量化值进化量控制器
 */
@RestController
@Slf4j
public class QuantifyEvolveController {

    @Autowired
    private QuantifyEvolveService quantifyEvolveService;

    /**
     * 查询量化值进化量, 分页显示
     */
    @PostMapping("/queryQuantifyEvolvePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryQuantifyEvolvePages(@RequestBody @Valid QuantifyEvolve param) {
        CommonPage<Map<String, Object>> commonPage = quantifyEvolveService.queryQuantifyEvolvePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    /**
     * 今日量化值进化量
     */
    @GetMapping("/todayTotalQuantifyEvolve")
    public CommonResult<String> todayTotalQuantifyEvolve() {
        String todayTotalQuantifyEvolve = quantifyEvolveService.todayTotalQuantifyEvolve();
        if (!todayTotalQuantifyEvolve.isEmpty()) {
            return CommonResult.success(todayTotalQuantifyEvolve);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 累计量化值进化量
     */
    @GetMapping("/totalQuantifyEvolve")
    public CommonResult<String> totalQuantifyEvolve() {
        String totalQuantifyEvolve = quantifyEvolveService.totalQuantifyEvolve();
        if (!totalQuantifyEvolve.isEmpty()) {
            return CommonResult.success(totalQuantifyEvolve);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 量化值进化量, 导出 Excel
     */
    @PostMapping("/exportQuantifyEvolveExcel")
    public void exportQuantifyEvolveExcel(HttpServletResponse response, QuantifyEvolve param) {
        try {
            // 获取量化值进化量数据
            List<Map<String, Object>> dataList = quantifyEvolveService.exportQuantifyEvolveExcel(param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String todayTotalQuantifyEvolve = quantifyEvolveService.todayTotalQuantifyEvolve();
            String totalQuantifyEvolve = quantifyEvolveService.totalQuantifyEvolve();

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("userName", "username");
            fieldMapping.put("quantifyEvolve", "quantifyEvolve");
            fieldMapping.put("quantifyEvolveTotal", "quantifyEvolveTotal");

            // 配置汇总信息（一行显示两个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日量化值进化量")
                        .value(todayTotalQuantifyEvolve)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计量化值进化量")
                        .value(totalQuantifyEvolve)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<QuantifyEvolve> config = UniversalExcelExporter.ExportConfig.<QuantifyEvolve>builder()
                    .dataList(dataList)
                    .entityClass(QuantifyEvolve.class)
                    .fileName("量化值进化量")
                    .sheetName("量化值进化量")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出量化值进化量异常", e);
        }
    }
}
