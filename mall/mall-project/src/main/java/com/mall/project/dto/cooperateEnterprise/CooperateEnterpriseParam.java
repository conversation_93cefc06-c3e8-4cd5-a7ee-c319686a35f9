package com.mall.project.dto.cooperateEnterprise;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class CooperateEnterpriseParam {

    //  企业ID
    @Min(1) // 最小值为1
    @Max(9999999999L) // 10位最大值
    private Long enterpriseId;

    private String id; //数据名称id,删除时使用

    private String phone;

    private String enterpriseIds;

    //  交易名称
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "交易名称不能包含特殊字符")
    @Size(max = 30, message = "交易名称长度不能超过30位")
    private String tradeName;

    //  企业产品数据ID
    @Min(1) // 最小值为1
    @Max(9999999999L) // 10位最大值
    Long enterpriseProductDataId;

    //  企业数据开关
    @Pattern(regexp = "^[01]$", message = "企业数据开关只能为0或1")
    private String enterpriseDataSwitch;

    //  各企业系统每日更新总累计量化数开关
    @Pattern(regexp = "^[01]$", message = "各企业系统每日更新总累计量化数开关只能为0或1")
    private String quantityDataSwitch;

    //  自定义常量
    @Pattern(regexp = "^-?\\d+$", message = "自定义常量只能为整数")
    private String customConstants;

    //  自定义常量开关
    @Pattern(regexp = "^[01]$", message = "自定义常量开关只能为0或1")
    private String customConstantsSwitch;

    // 查询量化率月份, 默认为当前月份 判断格式为  yyyy-MM
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "查询量化率月份格式不正确，请输入yyyy-MM格式")
    private String searchMonth;

    // 每笔交易金额
    private String perTradeAmount;

    // 每笔交易金额开关设置：0-开启，1-关闭
    @Pattern(regexp = "^[01]$", message = "每笔交易金额开关设置只能为0或1")
    private String onOff;

    // 验证开始时间格式 yyyy-MM-dd格式
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始时间格式不正确，请输入yyyy-MM-dd格式")
    private String startTime;

    // 验证结束时间 格式 yyyy-MM-dd格式
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束时间格式不正确，请输入yyyy-MM-dd格式")
    private String endTime;

    private Integer pageNum;
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
