package com.mall.project.dao.transactionDataTotal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class TransactionDataTotalDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 获取累计交易金额
    public double sumTradeAmountTotal() {
        // 构建查询语句
        String sql = "select sum(trade_amount) as trade_amount_total from enterprise_product_data WHERE status = '0'";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        if (!result.isEmpty()) {
            Map<String, Object> row = result.get(0);
            Object tradeAmountTotal = row.get("trade_amount_total");
            if (tradeAmountTotal != null) {
                return Double.parseDouble(tradeAmountTotal.toString());
            }
        }
        return 0.0;
    }

    // 查询统计累计交易数据导出
    public List<Map<String, Object>> transactionDataTotalExport(String startDate, String endDate) {
        // 构建查询语句
        String sql = "SELECT \n" +
                "    DATE(MAX(d.update_time)) as update_time, \n" +
                "    d.phone,\n" +
                "    e.name as enterprise_name,\n" +
                "    d.trade_name,\n" +
                "    SUM(CAST(d.trade_amount AS DECIMAL(15,2))) as trade_amount_sum,\n" +
                "    COUNT(*) as record_count\n" +
                "FROM enterprise_product_data d\n" +
                "LEFT JOIN cooperate_enterprise e ON d.enterprise_id = e.id\n" +
                "WHERE d.status = '0'  \n" +
                "    AND d.trade_name IS NOT NULL \n" +
                "    AND d.trade_amount IS NOT NULL\n";
        if (startDate != null && !startDate.isEmpty()) {
            sql += "    AND d.update_time >= '" + startDate + "'\n";
        }
        if (endDate != null && !endDate.isEmpty()) {
            sql += "    AND d.update_time <= '" + endDate + "'\n";
        }
        sql += "GROUP BY d.phone, e.name, d.trade_name\n" +
                "ORDER BY d.phone, trade_amount_sum DESC";
        return jdbcTemplate.queryForList(sql);
    }
    // 查询统计累计交易数据 分页显示
    public List<Map<String, Object>> QuerytransactionDataTotalPages(String phone, String startDate, String endTDate, int limit, int offset) {
        // 构建查询语句
        String sql = "SELECT \n" +
                "    DATE(MAX(d.update_time)) as update_time, \n" +
                "    d.phone,\n" +
                "    e.name as enterprise_name,\n" +
                "    d.trade_name,\n" +
                "    SUM(CAST(d.trade_amount AS DECIMAL(15,2))) as trade_amount_sum,\n" +
                "    COUNT(*) as record_count\n" +
                "FROM enterprise_product_data d\n" +
                "LEFT JOIN cooperate_enterprise e ON d.enterprise_id = e.id\n" +
                "WHERE d.status = '0'  \n" +
                "    AND d.trade_name IS NOT NULL \n" +
                "    AND d.trade_amount IS NOT NULL\n";
        if (phone != null && !phone.isEmpty()) {
            sql += "    AND d.phone LIKE '%" + phone + "%'\n";
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += "    AND d.update_time >= '" + startDate + "'\n";
        }
        if (endTDate != null && !endTDate.isEmpty()) {
            sql += "    AND d.update_time <= '" + endTDate + "'\n";
        }
        sql += "GROUP BY d.phone, e.name, d.trade_name\n" +
                "ORDER BY d.phone, trade_amount_sum DESC LIMIT " + limit + " OFFSET " + offset;
        return jdbcTemplate.queryForList(sql);
    }
    // 查询统计累计交易数据 分页显示 总数
    public int countTransactionDataTotal(String phone, String startDate, String endTDate) {
        // 构建查询语句
        String sql = "SELECT COUNT(*) FROM (SELECT \n" +
                "    DATE(MAX(d.update_time)) as update_time, \n" +
                "    d.phone,\n" +
                "    e.name as enterprise_name,\n" +
                "    d.trade_name,\n" +
                "    SUM(CAST(d.trade_amount AS DECIMAL(15,2))) as trade_amount_sum,\n" +
                "    COUNT(*) as record_count\n" +
                "FROM enterprise_product_data d\n" +
                "LEFT JOIN cooperate_enterprise e ON d.enterprise_id = e.id\n" +
                "WHERE d.status = '0'  \n" +
                "    AND d.trade_name IS NOT NULL \n" +
                "    AND d.trade_amount IS NOT NULL\n";
        if (phone != null && !phone.isEmpty()) {
            sql += "    AND d.phone LIKE '%" + phone + "%'\n";
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += "    AND d.update_time >= '" + startDate + "'\n";
        }
        if (endTDate != null && !endTDate.isEmpty()) {
            sql += "    AND d.update_time <= '" + endTDate + "'\n";
        }
        sql += "GROUP BY d.phone, e.name, d.trade_name) as subquery";
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }
}
