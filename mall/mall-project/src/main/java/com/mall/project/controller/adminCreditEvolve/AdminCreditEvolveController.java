package com.mall.project.controller.adminCreditEvolve;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.adminCreditEvolve.AdminCreditEvolve;
import com.mall.project.service.adminCreditEvolve.AdminCreditEvolveService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin量化进化量 控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class AdminCreditEvolveController {

    @Autowired
    private AdminCreditEvolveService adminCreditEvolveService;

    /**
     * 查询Admin量化进化量, 分页显示
     */
    @PostMapping("/queryAdminCreditEvolvePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryAdminCreditEvolvePages(@RequestBody @Valid AdminCreditEvolve param) {
        CommonPage<Map<String, Object>> commonPage = adminCreditEvolveService.queryAdminCreditEvolvePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNumOrDefault(), param.getPageSizeOrDefault());
        return CommonResult.success(commonPage);
    }

    /**
     * 累计Admin量化进化量
     */
    @GetMapping("/totalAdminCreditEvolve")
    public CommonResult<String> totalAdminCreditEvolve() {
        String totalAdminCreditEvolve = adminCreditEvolveService.totalAdminCreditEvolve();
        if (!totalAdminCreditEvolve.isEmpty()) {
            return CommonResult.success(totalAdminCreditEvolve);
        } else {
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 导出Admin量化进化量 Excel
     */
    @PostMapping("/exportAdminCreditEvolveExcel")
    public void exportAdminCreditEvolveExcel(HttpServletResponse response, AdminCreditEvolve param) {
        try {
            // 获取Admin量化进化量数据
            List<Map<String, Object>> dataList = adminCreditEvolveService.exportAdminCreditEvolveExcel(param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String totalAdminCreditEvolve = adminCreditEvolveService.totalAdminCreditEvolve();

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("value", "value");

            // 配置汇总信息
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                // 第一行：1个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计Admin量化进化量")
                        .value(totalAdminCreditEvolve)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<AdminCreditEvolve> config = UniversalExcelExporter.ExportConfig.<AdminCreditEvolve>builder()
                    .dataList(dataList)
                    .entityClass(AdminCreditEvolve.class)
                    .fileName("Admin量化进化量")
                    .sheetName("Admin量化进化量")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出Admin量化进化量异常", e);
        }
    }
}
